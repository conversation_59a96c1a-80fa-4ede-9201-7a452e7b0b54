import type { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ara<PERSON>s, JWTPayload } from "jose";

import { Effect } from "effect";
import { decodeProtectedHeader, importJWK, jwtVerify } from "jose";

import type { Jwk } from "../schemas/oidc-schema";

import {
	JwtClaimsSchema,
	type JwtValidationError,
} from "../schemas/jwt-claims-schema";
import { completeOidcEffect, findJwkByKidEffect } from "./oidc-config-effect";

/** Centralized error mapping utility */
const mapJwtError = (error: unknown): JwtValidationError => {
	if (error instanceof Error) {
		const message = error.message.toLowerCase();
		if (message.includes("expired")) return "JWT_EXPIRED";
		if (message.includes("issuer")) return "JWT_ISSUER_MISMATCH";
		if (message.includes("signature")) return "JWT_SIGNATURE_INVALID";
		if (message.includes("not yet valid")) return "JWT_NOT_YET_VALID";
		if (message.includes("claims")) return "JWT_CLAIMS_INVALID";
	}
	return "JWT_PARSE_ERROR";
};

/** Parse JWT header to extract key ID (kid) using jose's built-in decoder */
const parseJwtHeader = (accessToken: string) =>
	Effect.tryPromise({
		catch: () => "JWT_PARSE_ERROR" as const,
		try: async (): Promise<string | undefined> => {
			try {
				const header = decodeProtectedHeader(accessToken);
				return header.kid;
			} catch {
				// Fallback to manual parsing if jose fails
				const jwtParts = accessToken.split(".");
				if (jwtParts.length !== 3) {
					throw new Error("Invalid JWT format");
				}

				const headerPart = jwtParts[0];
				if (!headerPart) {
					throw new Error("Missing JWT header");
				}

				const header = JSON.parse(atob(headerPart)) as JWTHeaderParameters;
				return header.kid;
			}
		},
	});

/** Convert JWK to CryptoKey object for jose */
const importJwk = (jwk: Jwk) =>
	Effect.tryPromise({
		catch: () => "JWT_SIGNATURE_INVALID" as const,
		try: (): Promise<CryptoKey | Uint8Array> => importJWK(jwk as JWK),
	});

/** Verify JWT signature and extract payload with comprehensive validation */
const verifyJwtSignature = (
	accessToken: string,
	publicKey: CryptoKey | Uint8Array,
	issuerUrl: string,
) =>
	Effect.tryPromise({
		catch: (error) => {
			console.error("JWT verification failed:", error);
			return mapJwtError(error);
		},
		try: async (): Promise<JWTPayload> => {
			const { payload } = await jwtVerify(accessToken, publicKey, {
				issuer: issuerUrl,
				// Note: We don't validate audience here as it may vary
				// clockTolerance: 30, // Allow 30 seconds clock skew
			});
			return payload;
		},
	});

/** Validate JWT claims structure */
const validateJwtClaims = (payload: JWTPayload) =>
	Effect.tryPromise({
		catch: (error) => {
			console.error("JWT claims validation failed:", error);
			return "JWT_CLAIMS_INVALID" as const;
		},
		try: async () => JwtClaimsSchema.parse(payload),
	});

/**
 * JWT validation Effect that uses cached OIDC configuration
 *
 * This is the main validation function that performs comprehensive JWT
 * validation:
 *
 * 1. JWT parsing and structure validation
 * 2. Signature verification using OIDC public keys (cached)
 * 3. Claims validation (structure only - timing/issuer handled by jose)
 *
 * This Effect never fails - all errors are converted to validation results.
 *
 * @param accessToken - The JWT access token to validate
 * @returns Effect that resolves to validation result (never fails)
 */
export const validateAccessTokenJwtEffect = (accessToken: string) =>
	Effect.Do.pipe(
		// Parse JWT header to extract key ID
		Effect.bind("kid", () => parseJwtHeader(accessToken)),

		// Get OIDC configuration and JWKS
		Effect.bind("oidcData", () => completeOidcEffect),

		// Find the matching JWK by key ID
		Effect.bind("jwk", ({ kid, oidcData }) =>
			findJwkByKidEffect(oidcData.jwks, kid),
		),

		// Convert JWK to CryptoKey object for jose
		Effect.bind("publicKey", ({ jwk }) => importJwk(jwk)),

		// Verify JWT signature and extract payload (includes timing and issuer validation)
		Effect.bind("payload", ({ oidcData, publicKey }) =>
			verifyJwtSignature(accessToken, publicKey, oidcData.issuerUrl),
		),

		// Validate JWT claims structure
		Effect.bind("claims", ({ payload }) => validateJwtClaims(payload)),

		Effect.map(
			({ claims }) =>
				({
					claims,
					success: true,
				}) as const,
		),

		Effect.catchAll((error) =>
			Effect.succeed({
				error: typeof error === "string" ? error : "JWT_PARSE_ERROR",
				success: false,
			} as const),
		),
	);

/**
 * Creates appropriate login URL based on JWT validation error
 *
 * @param baseLoginUrl - The base login URL
 * @param error - The JWT validation error
 * @returns Complete login URL with appropriate parameters
 */
export const createLoginUrlForError = (
	baseLoginUrl: string,
	error: JwtValidationError,
): string => {
	const url = new URL(baseLoginUrl);

	// Skip the ?prompt=login when error is JWT_EXPIRED so that a possible existing session can refresh the token
	if (error !== "JWT_EXPIRED") {
		url.searchParams.set("prompt", "login");
	}

	return url.toString();
};
